import { Link, Outlet, useLoaderD<PERSON>, useRouteError } from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import { NavMenu } from "@shopify/app-bridge-react";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";
import { authenticate } from "../shopify.server";
import { setupShopifyHeaders } from "../utils/middleware.js";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = async ({ request }) => {
  try {
    // Authenticate the admin user
    await authenticate.admin(request);

    return {
      apiKey: process.env.SHOPIFY_API_KEY || "",
      appUrl: process.env.SHOPIFY_APP_URL || ""
    };
  } catch (error) {
    console.error("Authentication error:", error);
    throw new Response("Authentication failed", { status: 401 });
  }
};

export default function App() {
  const { apiKey, appUrl } = useLoaderData();

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey} forceRedirect={false}>
      <NavMenu>
        <Link to="/app" rel="home">
          Home
        </Link>
        <Link to="/app/orders">Orders</Link>
        <Link to="/app/analytics">Analytics</Link>
        <Link to="/app/tracking">Tracking</Link>
        <Link to="/app/settings">Settings</Link>
        <Link to="/app/riders">Riders</Link>
      </NavMenu>
      <Outlet />
    </AppProvider>
  );
}

// Shopify needs Remix to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers = (headersArgs) => {
  const headers = boundary.headers(headersArgs);
  return setupShopifyHeaders(headers);
};
